<template>
  <div class="h100">
    <router-view v-slot="{ Component }">
      <keep-alive :include="keepAliveNameList">
        <component :is="Component" :key="refreshRouterViewKey" class="w100" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onBeforeMount,
  onUnmounted,
  nextTick,
  watch
} from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from '/@/store/index'

export default defineComponent({
  name: 'layoutParentView',
  setup() {
    const { proxy } = getCurrentInstance() as any
    const route = useRoute()
    const store = useStore()
    const state: any = reactive({
      refreshRouterViewKey: null,
      keepAliveNameList: [],
      keepAliveNameNewList: [],
      forceRefreshComponents: new Set() // 记录需要强制刷新的组件
    })
    // 设置主界面切换动画
    const setTransitionName = computed(() => {
      return store.state.themeConfig.themeConfig.animation
    })
    // 获取布局配置信息
    const getThemeConfig = computed(() => {
      return store.state.themeConfig.themeConfig
    })
    // 获取组件缓存列表(name值)
    const getKeepAliveNames = computed(() => {
      return store.state.keepAliveNames.keepAliveNames
    })
    // 页面加载前，处理缓存，页面刷新时路由缓存处理
    onBeforeMount(() => {
      state.keepAliveNameList = getKeepAliveNames.value
      proxy.mittBus.on('onTagsViewRefreshRouterView', (fullPath: string) => {
        // 优化重载逻辑：使用时间戳确保组件完全重新创建，避免重复渲染
        const currentComponentName = route.name
        console.log('开始重载页面:', currentComponentName, fullPath)

        // 标记当前组件需要强制刷新
        state.forceRefreshComponents.add(currentComponentName)

        // 临时从 keep-alive 缓存中移除当前组件
        state.keepAliveNameList = getKeepAliveNames.value.filter(
          (name: string) => name !== currentComponentName
        )

        // 使用带时间戳的唯一key强制重新创建组件
        const refreshKey = `${fullPath}-refresh-${Date.now()}`
        state.refreshRouterViewKey = refreshKey

        // 在下一个tick中恢复缓存列表
        nextTick(() => {
          state.keepAliveNameList = getKeepAliveNames.value
          console.log('页面重载完成:', currentComponentName, refreshKey)
        })
      })

      // 监听强制清理缓存事件
      proxy.mittBus.on('onForceCleanCache', (componentName: string) => {
        console.log('强制清理缓存:', componentName)
        // 记录需要强制刷新的组件
        state.forceRefreshComponents.add(componentName)
        // 临时从 include 列表中移除组件
        state.keepAliveNameList = state.keepAliveNameList.filter((name) => name !== componentName)
        // 强制重新渲染
        state.refreshRouterViewKey = null
        nextTick(() => {
          // 恢复 include 列表
          state.keepAliveNameList = getKeepAliveNames.value
          state.refreshRouterViewKey = route.fullPath
        })
      })
    })
    // 页面卸载时
    onUnmounted(() => {
      proxy.mittBus.off('onTagsViewRefreshRouterView')
      proxy.mittBus.off('onForceCleanCache')
    })
    // 监听路由变化，防止 tagsView 多标签时，切换动画消失
    watch(
      () => route.fullPath,
      () => {
        // 检查当前路由的组件是否需要强制刷新
        const currentComponentName = route.name
        if (state.forceRefreshComponents.has(currentComponentName)) {
          console.log('检测到需要强制刷新的组件:', currentComponentName)
          // 使用带时间戳的 key 强制重新创建组件
          state.refreshRouterViewKey = `${route.fullPath}-refresh-${Date.now()}`
          // 从强制刷新列表中移除
          state.forceRefreshComponents.delete(currentComponentName)
        } else {
          // 正常路由切换，使用普通的fullPath作为key
          state.refreshRouterViewKey = route.fullPath
        }
      },
      { immediate: true }
    )
    return {
      getThemeConfig,
      getKeepAliveNames,
      setTransitionName,
      ...toRefs(state)
    }
  }
})
</script>
