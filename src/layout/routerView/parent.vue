<template>
  <div class="h100">
    <router-view v-slot="{ Component }">
      <keep-alive :include="keepAliveNameList">
        <component :is="Component" :key="refreshRouterViewKey" class="w100" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  toRefs,
  reactive,
  getCurrentInstance,
  onBeforeMount,
  onUnmounted,
  nextTick,
  watch
} from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from '/@/store/index'

export default defineComponent({
  name: 'layoutParentView',
  setup() {
    const { proxy } = getCurrentInstance() as any
    const route = useRoute()
    const store = useStore()
    const state: any = reactive({
      refreshRouterViewKey: null,
      keepAliveNameList: [],
      keepAliveNameNewList: [],
      forceRefreshComponents: new Set() // 记录需要强制刷新的组件
    })
    // 设置主界面切换动画
    const setTransitionName = computed(() => {
      return store.state.themeConfig.themeConfig.animation
    })
    // 获取布局配置信息
    const getThemeConfig = computed(() => {
      return store.state.themeConfig.themeConfig
    })
    // 获取组件缓存列表(name值)
    const getKeepAliveNames = computed(() => {
      return store.state.keepAliveNames.keepAliveNames
    })
    // 页面加载前，处理缓存，页面刷新时路由缓存处理
    onBeforeMount(() => {
      state.keepAliveNameList = getKeepAliveNames.value
      proxy.mittBus.on('onTagsViewRefreshRouterView', (fullPath: string) => {
        // 优化重载逻辑：确保组件完全重新创建，同时保持keep-alive正常工作
        const currentComponentName = route.name
        console.log('开始重载页面:', currentComponentName, fullPath)

        // 方法1：临时从 keep-alive 缓存中移除当前组件
        state.keepAliveNameList = getKeepAliveNames.value.filter(
          (name: string) => name !== currentComponentName
        )

        // 强制重新渲染组件
        state.refreshRouterViewKey = null

        // 在下一个tick中恢复缓存列表并设置正常的key
        nextTick(() => {
          state.keepAliveNameList = getKeepAliveNames.value
          state.refreshRouterViewKey = fullPath
          console.log('页面重载完成:', currentComponentName, '缓存已恢复')
        })
      })

      // 监听强制清理缓存事件
      proxy.mittBus.on('onForceCleanCache', (componentName: string) => {
        console.log('强制清理缓存:', componentName)
        // 记录需要强制刷新的组件
        state.forceRefreshComponents.add(componentName)
        // 临时从 include 列表中移除组件
        state.keepAliveNameList = state.keepAliveNameList.filter((name) => name !== componentName)
        // 强制重新渲染
        state.refreshRouterViewKey = null
        nextTick(() => {
          // 恢复 include 列表
          state.keepAliveNameList = getKeepAliveNames.value
          state.refreshRouterViewKey = route.fullPath
        })
      })
    })
    // 页面卸载时
    onUnmounted(() => {
      proxy.mittBus.off('onTagsViewRefreshRouterView')
      proxy.mittBus.off('onForceCleanCache')
    })
    // 监听路由变化，防止 tagsView 多标签时，切换动画消失
    watch(
      () => route.fullPath,
      () => {
        // 正常路由切换，使用普通的fullPath作为key，保持keep-alive正常工作
        if (state.refreshRouterViewKey !== null) {
          state.refreshRouterViewKey = route.fullPath
        }
      },
      { immediate: true }
    )

    // 监听 keep-alive 缓存列表变化，确保同步
    watch(
      () => getKeepAliveNames.value,
      (newNames) => {
        state.keepAliveNameList = newNames
        console.log('keep-alive 缓存列表已更新:', newNames)
      },
      { deep: true }
    )
    return {
      getThemeConfig,
      getKeepAliveNames,
      setTransitionName,
      ...toRefs(state)
    }
  }
})
</script>
