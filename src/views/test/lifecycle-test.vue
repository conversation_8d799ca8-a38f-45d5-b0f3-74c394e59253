<template>
  <div class="lifecycle-test-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>生命周期测试页面</span>
          <el-button type="primary" @click="refreshPage">手动刷新</el-button>
        </div>
      </template>
      
      <div class="lifecycle-logs">
        <h3>生命周期执行记录：</h3>
        <div class="log-item" v-for="(log, index) in lifecycleLogs" :key="index">
          <span class="timestamp">{{ log.timestamp }}</span>
          <span class="lifecycle-name" :class="log.type">{{ log.lifecycle }}</span>
          <span class="message">{{ log.message }}</span>
        </div>
      </div>
      
      <div class="test-info">
        <p><strong>组件名称：</strong>{{ componentName }}</p>
        <p><strong>创建时间：</strong>{{ createdTime }}</p>
        <p><strong>挂载时间：</strong>{{ mountedTime }}</p>
        <p><strong>更新次数：</strong>{{ updateCount }}</p>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onBeforeUnmount, onUnmounted, getCurrentInstance } from 'vue'

export default defineComponent({
  name: 'LifecycleTest',
  setup() {
    const { proxy } = getCurrentInstance() as any
    const componentName = 'LifecycleTest'
    const lifecycleLogs = ref([])
    const createdTime = ref('')
    const mountedTime = ref('')
    const updateCount = ref(0)
    
    // 添加生命周期日志
    const addLog = (lifecycle: string, message: string, type: string = 'info') => {
      const timestamp = new Date().toLocaleTimeString()
      lifecycleLogs.value.push({
        timestamp,
        lifecycle,
        message,
        type
      })
      console.log(`[${componentName}] ${lifecycle}: ${message}`)
    }
    
    // 手动刷新页面
    const refreshPage = () => {
      proxy.mittBus.emit('onTagsViewRefreshRouterView', proxy.$route.fullPath)
    }
    
    // 生命周期钩子
    onBeforeMount(() => {
      addLog('onBeforeMount', '组件即将挂载', 'warning')
    })
    
    onMounted(() => {
      const time = new Date().toLocaleTimeString()
      mountedTime.value = time
      addLog('onMounted', `组件已挂载 - ${time}`, 'success')
    })
    
    onBeforeUpdate(() => {
      addLog('onBeforeUpdate', '组件即将更新', 'warning')
    })
    
    onUpdated(() => {
      updateCount.value++
      addLog('onUpdated', `组件已更新 - 第${updateCount.value}次`, 'info')
    })
    
    onBeforeUnmount(() => {
      addLog('onBeforeUnmount', '组件即将卸载', 'error')
    })
    
    onUnmounted(() => {
      addLog('onUnmounted', '组件已卸载', 'error')
    })
    
    // 初始化
    createdTime.value = new Date().toLocaleTimeString()
    addLog('setup', `组件初始化 - ${createdTime.value}`, 'primary')
    
    return {
      componentName,
      lifecycleLogs,
      createdTime,
      mountedTime,
      updateCount,
      refreshPage
    }
  }
})
</script>

<style scoped lang="scss">
.lifecycle-test-container {
  padding: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .lifecycle-logs {
    margin-bottom: 20px;
    max-height: 400px;
    overflow-y: auto;
    
    .log-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
      
      .timestamp {
        width: 100px;
        color: #666;
        font-size: 12px;
      }
      
      .lifecycle-name {
        width: 150px;
        font-weight: bold;
        margin-right: 10px;
        
        &.primary { color: #409eff; }
        &.success { color: #67c23a; }
        &.warning { color: #e6a23c; }
        &.error { color: #f56c6c; }
        &.info { color: #909399; }
      }
      
      .message {
        flex: 1;
        color: #333;
      }
    }
  }
  
  .test-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    
    p {
      margin: 5px 0;
      color: #333;
    }
  }
}
</style>
