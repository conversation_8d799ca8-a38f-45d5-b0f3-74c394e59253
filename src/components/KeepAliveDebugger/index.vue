<template>
  <div class="keep-alive-debugger" v-if="showDebugger">
    <el-card class="debugger-card">
      <template #header>
        <div class="debugger-header">
          <span>Keep-Alive 调试器</span>
          <el-button size="small" @click="toggleDebugger">
            {{ showDebugger ? '隐藏' : '显示' }}
          </el-button>
        </div>
      </template>
      
      <div class="debugger-content">
        <div class="info-section">
          <h4>当前路由信息</h4>
          <p><strong>路由名称：</strong>{{ currentRoute.name }}</p>
          <p><strong>路由路径：</strong>{{ currentRoute.path }}</p>
          <p><strong>是否缓存：</strong>{{ currentRoute.meta?.isKeepAlive ? '是' : '否' }}</p>
          <p><strong>Router Key：</strong>{{ routerKey }}</p>
        </div>
        
        <div class="cache-section">
          <h4>Keep-Alive 缓存列表</h4>
          <div class="cache-list">
            <el-tag 
              v-for="name in keepAliveNames" 
              :key="name"
              :type="name === currentRoute.name ? 'success' : 'info'"
              class="cache-tag"
            >
              {{ name }}
            </el-tag>
          </div>
          <p class="cache-count">缓存组件数量: {{ keepAliveNames.length }}</p>
        </div>
        
        <div class="actions-section">
          <h4>调试操作</h4>
          <el-button size="small" @click="refreshCurrentPage">刷新当前页面</el-button>
          <el-button size="small" @click="clearAllCache" type="warning">清空所有缓存</el-button>
          <el-button size="small" @click="clearCurrentCache" type="danger">清空当前缓存</el-button>
        </div>
        
        <div class="logs-section">
          <h4>操作日志</h4>
          <div class="logs-container">
            <div v-for="(log, index) in logs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, getCurrentInstance, onMounted, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from '/@/store/index'

export default defineComponent({
  name: 'KeepAliveDebugger',
  setup() {
    const { proxy } = getCurrentInstance() as any
    const route = useRoute()
    const store = useStore()
    
    const showDebugger = ref(true)
    const logs = ref([])
    const routerKey = ref('')
    
    // 获取当前路由信息
    const currentRoute = computed(() => ({
      name: route.name,
      path: route.path,
      meta: route.meta
    }))
    
    // 获取 keep-alive 缓存列表
    const keepAliveNames = computed(() => {
      return store.state.keepAliveNames.keepAliveNames || []
    })
    
    // 添加日志
    const addLog = (message: string) => {
      const time = new Date().toLocaleTimeString()
      logs.value.unshift({ time, message })
      if (logs.value.length > 20) {
        logs.value = logs.value.slice(0, 20)
      }
      console.log(`[Keep-Alive Debug] ${message}`)
    }
    
    // 切换调试器显示
    const toggleDebugger = () => {
      showDebugger.value = !showDebugger.value
    }
    
    // 刷新当前页面
    const refreshCurrentPage = () => {
      addLog(`手动刷新页面: ${route.name}`)
      proxy.mittBus.emit('onTagsViewRefreshRouterView', route.fullPath)
    }
    
    // 清空所有缓存
    const clearAllCache = () => {
      addLog('清空所有 keep-alive 缓存')
      store.dispatch('keepAliveNames/setCacheKeepAlive', [])
    }
    
    // 清空当前组件缓存
    const clearCurrentCache = () => {
      const currentName = route.name
      addLog(`清空当前组件缓存: ${currentName}`)
      store.dispatch('keepAliveNames/removeCacheKeepAlive', currentName)
    }
    
    // 监听路由变化
    watch(
      () => route.fullPath,
      (newPath, oldPath) => {
        addLog(`路由变化: ${oldPath} -> ${newPath}`)
        routerKey.value = newPath
      },
      { immediate: true }
    )
    
    // 监听缓存列表变化
    watch(
      () => keepAliveNames.value,
      (newNames, oldNames) => {
        if (JSON.stringify(newNames) !== JSON.stringify(oldNames)) {
          addLog(`缓存列表更新: [${newNames.join(', ')}]`)
        }
      },
      { deep: true }
    )
    
    onMounted(() => {
      addLog('Keep-Alive 调试器已启动')
      
      // 监听重载事件
      proxy.mittBus.on('onTagsViewRefreshRouterView', (fullPath) => {
        addLog(`接收到重载事件: ${fullPath}`)
      })
    })
    
    onUnmounted(() => {
      proxy.mittBus.off('onTagsViewRefreshRouterView')
    })
    
    return {
      showDebugger,
      currentRoute,
      keepAliveNames,
      logs,
      routerKey,
      toggleDebugger,
      refreshCurrentPage,
      clearAllCache,
      clearCurrentCache
    }
  }
})
</script>

<style scoped lang="scss">
.keep-alive-debugger {
  position: fixed;
  top: 100px;
  right: 20px;
  width: 350px;
  z-index: 9999;
  
  .debugger-card {
    max-height: 600px;
    overflow-y: auto;
    
    .debugger-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .debugger-content {
      .info-section,
      .cache-section,
      .actions-section,
      .logs-section {
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #f0f0f0;
        
        h4 {
          margin: 0 0 10px 0;
          color: #333;
          font-size: 14px;
        }
        
        p {
          margin: 5px 0;
          font-size: 12px;
          color: #666;
        }
      }
      
      .cache-list {
        .cache-tag {
          margin: 2px;
        }
      }
      
      .cache-count {
        margin-top: 10px;
        font-weight: bold;
      }
      
      .actions-section {
        .el-button {
          margin: 2px;
        }
      }
      
      .logs-container {
        max-height: 200px;
        overflow-y: auto;
        
        .log-item {
          display: flex;
          margin-bottom: 5px;
          font-size: 11px;
          
          .log-time {
            width: 70px;
            color: #999;
            margin-right: 10px;
          }
          
          .log-message {
            flex: 1;
            color: #333;
          }
        }
      }
    }
  }
}
</style>
