/**
 * Keep-Alive 辅助工具
 * 用于调试和管理 keep-alive 缓存
 */

import { store } from '/@/store/index'
import { resetCacheInitFlag } from '/@/router/index'

export class KeepAliveHelper {
  /**
   * 获取当前缓存的组件列表
   */
  static getCachedComponents(): string[] {
    return store.state.keepAliveNames.keepAliveNames || []
  }

  /**
   * 检查指定组件是否被缓存
   */
  static isComponentCached(componentName: string): boolean {
    const cachedComponents = this.getCachedComponents()
    return cachedComponents.includes(componentName)
  }

  /**
   * 添加组件到缓存列表
   */
  static addToCache(componentName: string): void {
    store.dispatch('keepAliveNames/addCacheKeepAlive', componentName)
    console.log(`[Keep-Alive] 添加组件到缓存: ${componentName}`)
  }

  /**
   * 从缓存列表中移除组件
   */
  static removeFromCache(componentName: string): void {
    store.dispatch('keepAliveNames/removeCacheKeepAlive', componentName)
    console.log(`[Keep-Alive] 从缓存中移除组件: ${componentName}`)
  }

  /**
   * 清空所有缓存
   */
  static clearAllCache(): void {
    store.dispatch('keepAliveNames/setCacheKeepAlive', [])
    console.log('[Keep-Alive] 已清空所有缓存')
  }

  /**
   * 去重缓存列表
   */
  static deduplicateCache(): void {
    const cachedComponents = this.getCachedComponents()
    const uniqueComponents = [...new Set(cachedComponents)]

    if (cachedComponents.length !== uniqueComponents.length) {
      console.log('[Keep-Alive] 发现重复缓存，正在去重...')
      console.log('去重前:', cachedComponents)
      console.log('去重后:', uniqueComponents)
      store.dispatch('keepAliveNames/setCacheKeepAlive', uniqueComponents)
    } else {
      console.log('[Keep-Alive] 缓存列表无重复项')
    }
  }

  /**
   * 重置缓存列表（根据路由配置重新初始化）
   */
  static resetCache(): void {
    // 清空缓存列表
    this.clearAllCache()
    // 重置缓存初始化标志，允许重新初始化
    resetCacheInitFlag()
    console.log('[Keep-Alive] 缓存列表已重置，可重新初始化')
  }

  /**
   * 打印当前缓存状态
   */
  static printCacheStatus(): void {
    const cachedComponents = this.getCachedComponents()
    console.log('[Keep-Alive] 当前缓存状态:')
    console.log('缓存组件数量:', cachedComponents.length)
    console.log('缓存组件列表:', cachedComponents)
  }

  /**
   * 监听缓存变化
   */
  static watchCacheChanges(callback: (newCache: string[], oldCache: string[]) => void): void {
    let oldCache = [...this.getCachedComponents()]

    const unwatch = store.watch(
      (state) => state.keepAliveNames.keepAliveNames,
      (newCache: string[]) => {
        callback(newCache, oldCache)
        oldCache = [...newCache]
      },
      { deep: true }
    )

    return unwatch
  }
}

// 在开发环境下将工具挂载到 window 对象上，方便调试
if (import.meta.env.DEV) {
  ;(window as any).KeepAliveHelper = KeepAliveHelper
}

export default KeepAliveHelper
