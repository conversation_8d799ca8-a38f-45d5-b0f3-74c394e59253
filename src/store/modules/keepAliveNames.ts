import { Module } from 'vuex'
// 此处加上 `.ts` 后缀报错，具体原因不详
import { KeepAliveNamesState, RootStateTypes } from '/@/store/interface/index'

const keepAliveNamesModule: Module<KeepAliveNamesState, RootStateTypes> = {
  namespaced: true,
  state: {
    keepAliveNames: []
  },
  mutations: {
    // 设置路由缓存（name字段）
    getCacheKeepAlive(state: any, data: Array<string>) {
      state.keepAliveNames = data
    },
    // 临时移除指定组件的缓存
    removeCacheKeepAlive(state: any, componentName: string) {
      state.keepAliveNames = state.keepAliveNames.filter((name) => name !== componentName)
    },
    // 添加指定组件到缓存
    addCacheKeepAlive(state: any, componentName: string) {
      if (!state.keepAliveNames.includes(componentName)) {
        state.keepAliveNames.push(componentName)
      }
    }
  },
  actions: {
    // 设置路由缓存（name字段）
    async setCacheKeepAlive({ commit }, data: Array<string>) {
      commit('getCacheKeepAlive', data)
    },
    // 临时移除指定组件的缓存
    async removeCacheKeepAlive({ commit }, componentName: string) {
      commit('removeCacheKeepAlive', componentName)
    },
    // 添加指定组件到缓存
    async addCacheKeepAlive({ commit }, componentName: string) {
      commit('addCacheKeepAlive', componentName)
    }
  }
}

export default keepAliveNamesModule
